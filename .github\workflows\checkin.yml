name: AnyRouter 自动签到
on:
  schedule:
    - cron: '0 */6 * * *'
  workflow_dispatch:
  
jobs:
  checkin:
    runs-on: windows-latest
    environment: production
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Python 环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    # 缓存 pip 依赖
    - name: 缓存 pip 依赖
      uses: actions/cache@v4
      with:
        path: ~\AppData\Local\pip\Cache
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    # 缓存 Playwright 浏览器
    - name: 获取 Playwright 版本
      id: playwright-version
      run: |
        $version = python -c "import playwright; print(playwright.__version__ if hasattr(playwright, '__version__') else '1.0.0')"
        if (!$version) { $version = pip show playwright | Select-String "Version:" | ForEach-Object { $_.ToString().Split(":")[1].Trim() } }
        if (!$version) { $version = "unknown" }
        echo "version=$version" >> $env:GITHUB_OUTPUT
        
    - name: 缓存 Playwright 浏览器
      uses: actions/cache@v4
      id: playwright-cache
      with:
        path: ~\AppData\Local\ms-playwright
        key: ${{ runner.os }}-playwright-${{ steps.playwright-version.outputs.version }}
        restore-keys: |
          ${{ runner.os }}-playwright-
          
    - name: 安装 Playwright 浏览器
      if: steps.playwright-cache.outputs.cache-hit != 'true'
      run: |
        python -m playwright install chromium
        
    - name: 验证 Playwright 浏览器
      env:
        PYTHONIOENCODING: utf-8
      run: |
        python -c "from playwright.sync_api import sync_playwright; p = sync_playwright().start(); browser = p.chromium.launch(); browser.close(); p.stop(); print('Playwright验证成功')"
        
    - name: 执行签到
      env:
        PYTHONIOENCODING: utf-8
        ANYROUTER_ACCOUNTS: ${{ secrets.ANYROUTER_ACCOUNTS }}
        DINGDING_WEBHOOK: ${{ secrets.DINGDING_WEBHOOK }}
        EMAIL_USER: ${{ secrets.EMAIL_USER }}
        EMAIL_PASS: ${{ secrets.EMAIL_PASS }}
        EMAIL_TO: ${{ secrets.EMAIL_TO }}
        PUSHPLUS_TOKEN: ${{ secrets.PUSHPLUS_TOKEN }}
        SERVERPUSHKEY: ${{ secrets.SERVERPUSHKEY }}
        FEISHU_WEBHOOK: ${{ secrets.FEISHU_WEBHOOK }}
        WEIXIN_WEBHOOK: ${{ secrets.WEIXIN_WEBHOOK }}
      run: |
        python checkin.py
        
    - name: 显示执行结果
      if: always()
      run: |
        echo "签到任务执行完成"
        echo "时间: $(Get-Date)"
